@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

:root {
  --background: hsl(0 0% 94.1176%);
  --foreground: hsl(0 0% 20%);
  --card: hsl(0 0% 96.0784%);
  --card-foreground: hsl(0 0% 20%);
  --popover: hsl(0 0% 96.0784%);
  --popover-foreground: hsl(0 0% 20%);
  --primary: hsl(0 0% 37.6471%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(0 0% 87.8431%);
  --secondary-foreground: hsl(0 0% 20%);
  --muted: hsl(0 0% 85.098%);
  --muted-foreground: hsl(0 0% 40%);
  --accent: hsl(0 0% 75.2941%);
  --accent-foreground: hsl(0 0% 20%);
  --destructive: hsl(0 60% 50%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(0 0% 81.5686%);
  --input: hsl(0 0% 87.8431%);
  --ring: hsl(0 0% 37.6471%);
  --chart-1: hsl(0 0% 37.6471%);
  --chart-2: hsl(180 17.9191% 33.9216%);
  --chart-3: hsl(0 0% 56.4706%);
  --chart-4: hsl(0 0% 65.8824%);
  --chart-5: hsl(0 0% 75.2941%);

  --sidebar: hsl(0 0% 91.7647%);
  --sidebar-foreground: hsl(0 0% 20%);
  --sidebar-primary: hsl(0 0% 37.6471%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(0 0% 75.2941%);
  --sidebar-accent-foreground: hsl(0 0% 20%);
  --sidebar-border: hsl(0 0% 81.5686%);
  --sidebar-ring: hsl(0 0% 37.6471%);

  --font-sans: Montserrat, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Fira Code, monospace;

  --radius: 0.35rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(0 0% 20% / 0.07);
  --shadow-xs: 0px 2px 0px 0px hsl(0 0% 20% / 0.07);
  --shadow-sm: 0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 1px 2px -1px hsl(0 0% 20% / 0.15);
  --shadow: 0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 1px 2px -1px hsl(0 0% 20% / 0.15);
  --shadow-md: 0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 2px 4px -1px hsl(0 0% 20% / 0.15);
  --shadow-lg: 0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 4px 6px -1px hsl(0 0% 20% / 0.15);
  --shadow-xl: 0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 8px 10px -1px hsl(0 0% 20% / 0.15);
  --shadow-2xl: 0px 2px 0px 0px hsl(0 0% 20% / 0.38);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: hsl(0 0% 10.1961%);
  --foreground: hsl(0 0% 85.098%);
  --card: hsl(0 0% 12.549%);
  --card-foreground: hsl(0 0% 85.098%);
  --popover: hsl(0 0% 12.549%);
  --popover-foreground: hsl(0 0% 85.098%);
  --primary: hsl(0 0% 62.7451%);
  --primary-foreground: hsl(0 0% 10.1961%);
  --secondary: hsl(0 0% 18.8235%);
  --secondary-foreground: hsl(0 0% 85.098%);
  --muted: hsl(0 0% 16.4706%);
  --muted-foreground: hsl(0 0% 50.1961%);
  --accent: hsl(0 0% 25.098%);
  --accent-foreground: hsl(0 0% 85.098%);
  --destructive: hsl(0 66.3043% 63.9216%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(0 0% 20.7843%);
  --input: hsl(0 0% 18.8235%);
  --ring: hsl(0 0% 62.7451%);
  --chart-1: hsl(0 0% 62.7451%);
  --chart-2: hsl(187.0588 15.1786% 56.0784%);
  --chart-3: hsl(0 0% 43.9216%);
  --chart-4: hsl(0 0% 34.5098%);
  --chart-5: hsl(0 0% 25.098%);
  --sidebar: hsl(0 0% 12.1569%);
  --sidebar-foreground: hsl(0 0% 85.098%);
  --sidebar-primary: hsl(0 0% 62.7451%);
  --sidebar-primary-foreground: hsl(0 0% 10.1961%);
  --sidebar-accent: hsl(0 0% 25.098%);
  --sidebar-accent-foreground: hsl(0 0% 85.098%);
  --sidebar-border: hsl(0 0% 20.7843%);
  --sidebar-ring: hsl(0 0% 62.7451%);

  --font-sans: Inter, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Fira Code, monospace;
  --radius: 0.35rem;

  --shadow-2xs: 0px 2px 0px 0px hsl(0 0% 20% / 0.07);
  --shadow-xs: 0px 2px 0px 0px hsl(0 0% 20% / 0.07);
  --shadow-sm: 0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 1px 2px -1px hsl(0 0% 20% / 0.15);
  --shadow: 0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 1px 2px -1px hsl(0 0% 20% / 0.15);
  --shadow-md: 0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 2px 4px -1px hsl(0 0% 20% / 0.15);
  --shadow-lg: 0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 4px 6px -1px hsl(0 0% 20% / 0.15);
  --shadow-xl: 0px 2px 0px 0px hsl(0 0% 20% / 0.15), 0px 8px 10px -1px hsl(0 0% 20% / 0.15);
  --shadow-2xl: 0px 2px 0px 0px hsl(0 0% 20% / 0.38);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
