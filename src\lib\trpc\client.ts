import { QueryClient } from "@tanstack/react-query";
import { createTRPC<PERSON>ontext } from "@trpc/tanstack-react-query";

import type { Router } from "./root";

export const { TRPCProvider, useTRPC, useTRPCClient } = createTRPCContext<Router>();

function makeQueryClient() {
  return new QueryClient({ defaultOptions: { queries: { staleTime: 60 * 1000 } } });
}

let browserQueryClient: QueryClient | undefined = undefined;
export function getQueryClient() {
  if (typeof window === "undefined") {
    return makeQueryClient();
  } else {
    if (!browserQueryClient) browserQueryClient = makeQueryClient();
    return browserQueryClient;
  }
}
