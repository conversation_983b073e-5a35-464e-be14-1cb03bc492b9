import { trpcServer } from "@hono/trpc-server";
import { <PERSON><PERSON> } from "hono";
import { cors } from "hono/cors";
import { secureHeaders } from "hono/secure-headers";

import index from "@/frontend/index.html";

import { loggerMiddleware } from "./lib/middlewares/logger";
import { trimTrailingSlash } from "./lib/middlewares/requests/trim-trailing";

import { migrateDatabase } from "./shared/database/helpers";
import { keysPool } from "./shared/key-management/keys-pool";

import { createContext } from "./lib/trpc";
import { router } from "./lib/trpc/root";

import { proxyRoute } from "./proxy/route";

import { config } from "./config";
import { logger } from "./logger";

const app = new Hono()
  .use(cors())
  .use(trimTrailingSlash())
  .use(secureHeaders())
  .use(loggerMiddleware())
  .use((ctx, next) => {
    ctx.set("logger", logger.child({ module: "server" }));
    ctx.set("keyPool", keysPool);
    return next();
  })

  .use("/trpc/*", trpcServer({ router, createContext }));

app.route(config.proxyEndpointRoute, proxyRoute);

const server = Bun.serve({
  routes: {
    "/": index,
    "/admin": index,
    "/admin/*": index,
  },

  fetch: app.fetch,
  development: process.env.NODE_ENV !== "production" && { hmr: true, console: true },
});

await migrateDatabase();

logger.info(`🚀 Server running at ${server.url} - Env: ${config.enviroment}`);
await keysPool.init();
