import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Clamps a number between a minimum and maximum value.
 *
 * @param params An object containing the max, min, and input values.
 * @param params.max The maximum value.
 * @param params.min The minimum value.
 * @param params.input The number to clamp.
 * @returns The clamped number, which will be within the [min, max] range.
 */
export function withinRange({
  input,
  min,
  max,
}: {
  max: number;
  min: number;
  input: number;
}): number {
  return Math.max(min, Math.min(input, max));
}

export function assertNever(x: never): never {
  throw new Error(`Called assertNever with argument ${x}.`);
}

export const ObjectTyped = {
  keys: <TObject extends Record<string, unknown>>(obj: TObject) => {
    return Object.freeze(Object.keys(obj)) as readonly [keyof TObject];
  },
  entries: <TObject extends Record<string, unknown>>(obj: TObject) => {
    return Object.freeze(Object.entries(obj)) as readonly [keyof TObject, TObject[keyof TObject]][];
  },
  values: <TObject extends Record<string, unknown>>(obj: TObject) => {
    return Object.freeze(Object.values(obj)) as readonly TObject[keyof TObject][];
  },
};

export function normalizeForDiff(value: unknown): any {
  if (value instanceof Map) {
    return Object.fromEntries(
      Array.from(value.entries()).map(([k, v]) => [k, normalizeForDiff(v)]),
    );
  }

  if (value instanceof Set) {
    return Array.from(value.values()).map(normalizeForDiff);
  }

  if (Array.isArray(value)) {
    return value.map(normalizeForDiff);
  }

  if (value !== null && typeof value === "object") {
    return Object.fromEntries(Object.entries(value).map(([k, v]) => [k, normalizeForDiff(v)]));
  }

  return value;
}
