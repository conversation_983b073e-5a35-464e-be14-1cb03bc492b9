import type { Context as HonoContext } from "hono";

import { initTRPC, TRPCError } from "@trpc/server";
import type { FetchCreateContextFnOptions } from "@trpc/server/adapters/fetch";

import superjson from "superjson";

import { db } from "@/shared/database";
import { getAccessFromCookie } from "@/shared/utils/jwt";

export const createContext = async (opts: FetchCreateContextFnOptions, honoCtx: HonoContext) => {
  return { ...opts, honoCtx, db };
};

export type Context = Awaited<ReturnType<typeof createContext>>;
export const t = initTRPC.context<Context>().create({
  transformer: superjson,
});

export const isAuthenticated = t.middleware(async ({ ctx, next }) => {
  const access = await getAccessFromCookie(ctx.honoCtx);
  if (!access || access.type !== "access") {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  return next({ ctx: { ...ctx, user: access } });
});

export const publicProcedure = t.procedure;
export const protectedProcedure = publicProcedure.use(isAuthenticated);
