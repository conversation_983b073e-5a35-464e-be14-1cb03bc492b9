import { useQuery } from "@tanstack/react-query";
import { BoxesIcon, KeyIcon, LayoutGridIcon, UserPlusIcon, UsersIcon } from "lucide-react";
import { Link, Navigate, Outlet, useLocation } from "react-router";

import { Separator } from "@/components/ui/separator";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarRail,
} from "@/components/ui/sidebar";
import { Icons } from "@/components/ui/icons";

import { useTRPC } from "@/lib/trpc/client";
import { LLM_PROVIDERS, type LLM_Providers } from "@/shared/providers";

const providerDisplay: Record<
  LLM_Providers,
  { label: string; icon: React.ComponentType<{ className?: string }> }
> = {
  openai: { label: "OpenAI", icon: Icons.openai },
  anthropic: { label: "Anthropic", icon: Icons.anthropic },
  gemini: { label: "Gemini", icon: Icons.gemini },
  deepseek: { label: "DeepSeek", icon: Icons.deepseek },
  xai: { label: "XAI", icon: Icons.xai },
  groq: { label: "Groq", icon: Icons.groq },
};

function NavItem(props: {
  to: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}) {
  const location = useLocation();
  const isActive = location.pathname === props.to;
  return (
    <SidebarMenuItem>
      <SidebarMenuButton asChild isActive={isActive}>
        <Link to={props.to} className="flex items-center gap-2">
          <props.icon className="h-4 w-4" />
          <span>{props.label}</span>
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}

export function AdminLayout() {
  const trpc = useTRPC();
  const { data, isPending, isSuccess } = useQuery(trpc.auth.isAuthenticated.queryOptions());

  if (isPending) return <div>Loading...</div>;
  if (isSuccess && !data) return <Navigate to="/admin/login" replace />;

  return (
    <SidebarProvider>
      <Sidebar className="border-r">
        <SidebarHeader>
          <div className="px-2 py-2 text-sm font-semibold flex items-center gap-2">
            <LayoutGridIcon className="h-4 w-4" />
            <span>Admin</span>
          </div>
        </SidebarHeader>

        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>Users management</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <NavItem to="/admin/users" label="Users" icon={UsersIcon} />
                <NavItem to="/admin/users/create" label="Create user" icon={UserPlusIcon} />
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          <SidebarGroup>
            <SidebarGroupLabel>Keys management</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {LLM_PROVIDERS.map((provider) => (
                  <NavItem
                    key={provider}
                    to={`/admin/providers/${provider}`}
                    label={providerDisplay[provider].label}
                    icon={providerDisplay[provider].icon}
                  />
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          <SidebarGroup>
            <SidebarGroupLabel>Models management</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <NavItem to="/admin/models" label="Models" icon={BoxesIcon} />
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

        <SidebarFooter>
          <div className="px-2 py-2 text-xs text-muted-foreground">v1</div>
        </SidebarFooter>

        <SidebarRail />
      </Sidebar>

      <SidebarInset>
        <div className="flex h-full flex-col">
          <div className="h-10 flex items-center px-4">
            <div className="text-sm text-muted-foreground">Admin panel</div>
          </div>

          <Separator />
          <div className="flex-1 p-4">
            <Outlet />
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
