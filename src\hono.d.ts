import type pino from "pino";

import type { Key } from "./shared/key-management";
import type { KeysPool } from "./shared/key-management/keys-pool";
import type { LLM_Providers } from "./shared/providers";

declare module "hono" {
  interface ContextVariableMap {
    token: string;
    logger: pino.Logger;
    keyPool: KeysPool;

    requestData?: {
      model: string;
      selectedKey?: Key;
      streaming: boolean;
      provider: LLM_Providers;
    };
  }
}
