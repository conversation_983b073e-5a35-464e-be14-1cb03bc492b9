import "./styles/globals.css";

import { StrictMode, useState } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter, Navigate, Route, Routes } from "react-router";

import { QueryClientProvider } from "@tanstack/react-query";
import { createTRPCClient, httpBatchStreamLink, loggerLink } from "@trpc/client";

import SuperJSON from "superjson";

import { getQueryClient, TRPCProvider } from "@/lib/trpc/client";
import type { Router } from "@/lib/trpc/root";

import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";

import { HomePage } from "./routes/home";

import { AdminPage } from "./routes/admin";
import { AdminLayout } from "./routes/admin/_layout";
import { AdminLoginPage } from "./routes/admin/login";

const elem = document.getElementById("root")!;

function Root() {
  const queryClient = getQueryClient();
  const [trpcClient] = useState(() =>
    createTRPCClient<Router>({
      links: [
        loggerLink({
          enabled: (op) =>
            process.env.NODE_ENV === "development" ||
            (op.direction === "down" && op.result instanceof Error),
        }),
        httpBatchStreamLink({ transformer: SuperJSON, url: "http://localhost:3000/trpc" }),
      ],
    }),
  );

  return (
    <StrictMode>
      <QueryClientProvider client={queryClient}>
        <TRPCProvider trpcClient={trpcClient} queryClient={queryClient}>
          <ThemeProvider defaultTheme="dark" storageKey="ui-theme">
            <BrowserRouter>
              <Routes>
                <Route index element={<HomePage />} />

                <Route path="/admin/login" element={<AdminLoginPage />} />

                <Route path="/admin" element={<AdminLayout />}>
                  <Route path="/admin" element={<AdminPage />} />
                  <Route path="/admin/*" element={<Navigate to="/admin/dashboard" replace />} />
                </Route>
              </Routes>
            </BrowserRouter>

            <Toaster />
          </ThemeProvider>
        </TRPCProvider>
      </QueryClientProvider>
    </StrictMode>
  );
}

const app = <Root />;

if (import.meta.hot) {
  const root = (import.meta.hot.data.root ??= createRoot(elem));
  root.render(app);
} else {
  createRoot(elem).render(app);
}
