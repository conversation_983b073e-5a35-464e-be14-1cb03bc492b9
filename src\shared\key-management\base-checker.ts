import schedule, { Job } from "node-schedule";

import { tryCatch, tryCatchSync } from "@/shared/utils/try-catch";

import { logger } from "@/logger";
import { config } from "@/config";

import type { LLM_Providers } from "../providers";
import { db, schema, sql } from "../database";

import type { Key } from ".";
import { CheckFailedError } from "./error";
import { BaseKeyProvider } from "./base-provider";

export type KeyCheckerOptions = {
  /** The provider this key checker is for. */
  provider: LLM_Providers;

  /** Minimum time between checks for an individual key. */
  keyCheckPeriod: number;

  /** Minimum time between global check runs. */
  minCheckInterval: number;

  /** Function to update a key in the database from the provider. */
  updateFn: typeof BaseKeyProvider.prototype.update;

  /**
   * Maximum number of keys to check simultaneously.
   * @default 12
   */
  keyCheckBatchSize?: number;
};

export abstract class KeyCheckerBase<TKey extends Key> {
  protected readonly provider: LLM_Providers;
  protected readonly keyCheckBatchSize: number;
  protected readonly minCheckInterval: number;
  protected readonly keyCheckPeriod: number;

  protected readonly updateFn: BaseKeyProvider<Key>["update"];

  protected logger;
  protected scheduledJob?: Job;
  protected lastGlobalCheckedAt = 0;
  protected started = false;

  protected constructor(opts: KeyCheckerOptions) {
    this.updateFn = opts.updateFn;

    this.provider = opts.provider;
    this.keyCheckPeriod = opts.keyCheckPeriod;
    this.minCheckInterval = opts.minCheckInterval;
    this.keyCheckBatchSize = opts.keyCheckBatchSize ?? 12;

    this.logger = logger.child({ module: `key-checker-${opts.provider}` });
  }

  public start() {
    if (this.started) {
      this.logger.debug("Key checker already started; ignoring start() call.");
      return;
    }

    this.started = true;
    this.logger.info(`Starting key checker for ${this.provider}...`);
    this.scheduleNextRun(new Date(Date.now() + 10));
  }

  public stop() {
    if (this.scheduledJob) {
      this.logger.debug("Stopping key checker...");

      const [, err] = tryCatchSync(() => this.scheduledJob?.cancel());
      if (err) this.logger.error({ err }, "Failed to cancel scheduled job.");

      this.scheduledJob = undefined;
    }
    this.started = false;
  }

  /**
   * Schedules the next check. If there are still keys yet to be checked, it
   * will schedule a check immediately for the next unchecked key. Otherwise,
   * it will schedule a check for the least recently checked key, respecting
   * the minimum check interval.
   */
  public async scheduleNextRun(at?: Date) {
    const now = Date.now();

    if (!this.started) {
      this.logger.debug("scheduleNextRun() called before start; ignoring.");
      return;
    }

    const callId = Math.random().toString(36).slice(2, 8);
    const timeoutId = this.scheduledJob?.name;
    const checkLog = this.logger.child({ callId, timeoutId });

    const enabledKeys = await db.query.keys.findMany({
      where: sql`provider = ${this.provider} AND status != 'disabled' AND status != 'revoked'`,
      orderBy: (table, { asc }) => [asc(table.lastCheckedAt)],
    });

    // Only get keys that haven't been recheck for the minimum check interval
    const uncheckedKeys = enabledKeys.filter(
      (key) => key.lastCheckedAt < now - this.keyCheckPeriod,
    );

    const workingNumber = enabledKeys.length;
    const uncheckedNumber = uncheckedKeys.length;

    this.scheduledJob?.cancel();
    this.scheduledJob = undefined;

    if (!workingNumber) {
      checkLog.warn("All keys are disabled. Stopping.");
      return;
    }

    checkLog.debug({ workingNumber, uncheckedNumber }, "Scheduling next check...");

    if (uncheckedNumber === 0) {
      await this.checkingRecheckKey(enabledKeys.filter((k) => !!k.nextCheckAt) as TKey[], checkLog);
      return;
    }

    const keycheckBatch = uncheckedKeys.slice(0, this.keyCheckBatchSize) as TKey[];

    schedule.scheduleJob(
      `${this.provider}:checker:batch:${callId}`,
      new Date(now + 1000),
      async () => {
        await Promise.all(keycheckBatch.map((key) => this.checkKeySafe(key, checkLog)))
          .then(() => this.scheduleNextRun())
          .catch((err) => checkLog.error({ err }, "Failed to schedule next check."));
      },
    );

    checkLog.info(
      {
        batch: keycheckBatch.map((k) => k.hash),
        remaining: uncheckedKeys.length - keycheckBatch.length,
      },
      "Scheduled batch of initial checks.",
    );
    return;
  }

  protected async checkingRecheckKey(keys: TKey[], checkLog: typeof this.logger) {
    const now = Date.now();

    if (keys.length === 0) {
      checkLog.debug("No keys to recheck. Stopping.");
      return;
    }

    // Schedule the next check for the oldest key.
    const oldestKey = keys
      .filter((k) => !!k.nextCheckAt)
      .reduce((oldest, key) => (key.nextCheckAt! < oldest.nextCheckAt! ? key : oldest));

    // Don't check any individual key too often.
    // Don't check anything at all more frequently than some minimum interval
    // even if keys still need to be checked.
    const nextCheck = Math.max(
      oldestKey.nextCheckAt!,
      this.lastGlobalCheckedAt + this.minCheckInterval,
    );

    const baseDelay = nextCheck - now;
    const jitter = (Math.random() - 0.5) * baseDelay * 0.5;
    const jitteredDelay = Math.max(1000, baseDelay + jitter);

    this.scheduledJob = schedule.scheduleJob(
      `key-checker:recheck:${oldestKey.hash}`,
      new Date(now + jitteredDelay),
      async () => await this.checkKeySafe(oldestKey).then(() => this.scheduleNextRun()),
    );

    checkLog.debug(
      {
        key: oldestKey.hash,
        name: this.scheduledJob?.name,
        nextCheck: new Date(now + jitteredDelay),
        addedDelay: Math.floor((jitteredDelay - baseDelay) / 1000),
      },
      "Scheduled next recurring check.",
    );
  }

  /**
   * Safe wrapper for checking a key that ensures errors are handled and logged,
   * and that global timestamps are updated.
   */
  protected async checkKeySafe(key: TKey, log = this.logger): Promise<void> {
    log.debug({ key: key.hash }, "Checking key...");
    let updates: Partial<TKey> = {};

    const [data, testingError] = await tryCatch(this.testKeyOrFail(key), CheckFailedError);
    if (testingError) updates = await this.handleCheckingError(key, testingError);
    else updates = data;

    await this.updateFn(key.hash, { ...updates, lastCheckedAt: Date.now() });

    this.lastGlobalCheckedAt = Date.now();
  }

  protected abstract createHeaders(key: TKey, extras?: Record<string, string>): Headers;

  protected abstract testKeyOrFail(key: TKey): Promise<Partial<TKey>>;

  protected abstract handleCheckingError(
    key: TKey,
    error: Error | CheckFailedError,
  ): Promise<Partial<TKey>>;
}
